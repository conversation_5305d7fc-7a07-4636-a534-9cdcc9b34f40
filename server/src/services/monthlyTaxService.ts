import MonthlyTaxRepository from "../repositories/monthlyTaxRepository"
import { MonthlyTaxParams, PurchaseTaxData, SelloutTaxData, MonthlyTaxResponse } from "../models/MonthlyTax"

class MonthlyTaxService {
  private monthlyTaxRepository: MonthlyTaxRepository

  constructor(monthlyTaxRepository: MonthlyTaxRepository) {
    this.monthlyTaxRepository = monthlyTaxRepository
  }

  async getMonthlyTaxData(params: MonthlyTaxParams): Promise<MonthlyTaxResponse> {
    try {
      // Get purchase tax data
      const purchaseRawData = await this.monthlyTaxRepository.getPurchaseTaxData(params)
      const purchaseData = this.processPurchaseTaxData(purchaseRawData)

      // Get tax invoice data
      const selloutRawData = await this.monthlyTaxRepository.getTaxInvoiceData(params)
      const selloutData = this.processSelloutTaxData(selloutRawData)

      return {
        purchaseData,
        selloutData
      }
    } catch (error: any) {
      console.error("[MonthlyTaxService.getMonthlyTaxData] ❌ Error:", error.message)
      throw error
    }
  }

  private processPurchaseTaxData(rawData: any[]): PurchaseTaxData[] {
    return rawData.map(item => {
      const buyin = item.car_buyin || {}

      // Calculate total purchase cost
      const purchasePrice = buyin.purchase_price || 0
      const purchaseVatPercent = buyin.purchase_vat_percent || 0
      const operationCostInclVat = buyin.operation_cost_incl_vat || 0
      const transport1AuctionLot = buyin.transport_1_auction_lot || 0
      const initialCheck = buyin.initial_check || 0
      const taxInsuranceCostZero = buyin.tax_insurance_cost_zero || 0
      const otherCostsSeven = buyin.other_costs_seven || 0
      const fiveThreeTaxPercentage = buyin.five_three_tax_percentage || 0

      const totalPurchaseCost =
        purchasePrice +
        purchaseVatPercent +
        operationCostInclVat +
        transport1AuctionLot +
        initialCheck +
        taxInsuranceCostZero +
        otherCostsSeven +
        fiveThreeTaxPercentage

      return {
        id: item.car_id,
        indexNumber: item.index_number || '',
        carStatus: item.car_status || '',
        purchase_date: buyin.purchase_date || '',
        brand: buyin.brand || '',
        model: buyin.model || '',
        color: buyin.color || '',
        year: buyin.year || 0,
        old_license_plate: item.old_license_plate || '',
        vat_percent: buyin.vat_percent || 0,
        purchase_price: purchasePrice,
        purchase_vat_percent: purchaseVatPercent,
        operation_cost_incl_vat: operationCostInclVat,
        transport_1_auction_lot: transport1AuctionLot,
        initial_check: initialCheck,
        tax_insurance_cost_zero: taxInsuranceCostZero,
        other_costs_seven: otherCostsSeven,
        five_three_tax_percentage: fiveThreeTaxPercentage,
        total_purchase_cost: totalPurchaseCost
      }
    })
  }

  private processSelloutTaxData(rawData: any[]): SelloutTaxData[] {
    return rawData.map(item => {
      // Calculate VAT-related values
      const carCommissionAmount = item.car_commission_amount || 0
      const inputVatCommission = carCommissionAmount * 0.07 // 7% of commission amount
      const withholdingTax = carCommissionAmount * 0.03 // 3% withholding tax
      const vatPercent = 7 // Standard VAT for sales

      return {
        id: item.car_id,
        indexNumber: item.index_number || '',
        sale_date: item.sale_date || '',
        finance_received_date: item.finance_received_date || '',
        car_tax_invoice_date: item.car_tax_invoice_date || '',
        car_tax_invoice_number: item.car_tax_invoice_number || '',
        car_amount: item.car_amount || 0,
        car_vat_amount: item.car_vat_amount || 0,
        commission_tax_invoice_date: item.commission_tax_invoice_date || '',
        commission_tax_invoice_number: item.commission_tax_invoice_number || '',
        car_commission_amount: carCommissionAmount,
        input_vat_commission: inputVatCommission,
        withholding_tax: withholdingTax,
        vat_percent: vatPercent,
        tank_number: item.tank_number || ''
      }
    })
  }
}

export default MonthlyTaxService
