"use client"

import { useState, useEffect } from "react"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function DatePickerTest() {
  const [date1, setDate1] = useState<string>("2566-04-28") // Buddhist year date from database
  const [date2, setDate2] = useState<string>("") // Empty date for testing manual input
  const [submittedDates, setSubmittedDates] = useState<{date1: string, date2: string}>({
    date1: "",
    date2: ""
  })

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setSubmittedDates({
      date1,
      date2
    })

    // Log detailed information about the dates
    console.log("=== DATE PICKER TEST SUBMISSION ===")
    console.log("Date 1 (pre-loaded with Buddhist year):", date1)
    console.log("Date 2 (manually entered):", date2)

    // Parse the dates to show their components
    if (date1) {
      const date1Obj = new Date(date1)
      console.log("Date 1 parsed:", {
        fullYear: date1Obj.getFullYear(),
        month: date1Obj.getMonth() + 1,
        day: date1Obj.getDate(),
        isoString: date1Obj.toISOString()
      })
    }

    if (date2) {
      const date2Obj = new Date(date2)
      console.log("Date 2 parsed:", {
        fullYear: date2Obj.getFullYear(),
        month: date2Obj.getMonth() + 1,
        day: date2Obj.getDate(),
        isoString: date2Obj.toISOString()
      })
    }
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">ThaiDatePickerWrapper Test</h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Date Handling</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h2 className="text-lg font-semibold mb-2">Date from Database (Buddhist Era)</h2>
              <p className="text-sm text-gray-500 mb-2">
                This date is pre-loaded with a Buddhist Era date (2566-04-28) to simulate data from the database.
              </p>
              <ThaiDatePickerWrapper
                id="date1"
                label="Date 1 (Pre-loaded with Buddhist Era date)"
                value={date1}
                onChange={setDate1}
              />
              <div className="mt-2">
                <p className="text-sm">Current value: <code>{date1}</code></p>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-2">Empty Date (For Manual Input)</h2>
              <p className="text-sm text-gray-500 mb-2">
                This date is empty. Try entering a date manually or using the calendar.
              </p>
              <ThaiDatePickerWrapper
                id="date2"
                label="Date 2 (Empty)"
                value={date2}
                onChange={setDate2}
              />
              <div className="mt-2">
                <p className="text-sm">Current value: <code>{date2}</code></p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Button type="submit" className="mt-4">Submit Dates</Button>

        {submittedDates.date1 || submittedDates.date2 ? (
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Submitted Values</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Date 1: <code>{submittedDates.date1}</code></p>
              <p>Date 2: <code>{submittedDates.date2}</code></p>
            </CardContent>
          </Card>
        ) : null}
      </form>
    </div>
  )
}
