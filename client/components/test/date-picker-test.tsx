"use client"

import { useState, useEffect } from "react"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import dayjs from "dayjs"
import "dayjs/locale/th"
import buddhistEra from "dayjs/plugin/buddhistEra"

dayjs.extend(buddhistEra)
dayjs.locale("th")

interface TestScenario {
  id: string
  name: string
  description: string
  initialValue: string
  expectedBehavior: string
}

export function DatePickerTest() {
  // Test scenarios for comprehensive testing
  const testScenarios: TestScenario[] = [
    {
      id: "buddhist_era_db",
      name: "Buddhist Era from Database",
      description: "Simulates date loaded from database in Buddhist Era format",
      initialValue: "2566-04-28", // Buddhist year 2566 = Gregorian 2023
      expectedBehavior: "Should display as 28/04/2023 in input field"
    },
    {
      id: "buddhist_era_high",
      name: "High Buddhist Era Year",
      description: "Tests handling of higher Buddhist Era years",
      initialValue: "2570-12-31", // Buddhist year 2570 = Gregorian 2027
      expectedBehavior: "Should display as 31/12/2027 in input field"
    },
    {
      id: "empty_manual",
      name: "Empty for Manual Input",
      description: "Empty field for testing manual date entry",
      initialValue: "",
      expectedBehavior: "User can type Gregorian dates (e.g., 15/06/2024) which converts to Buddhist Era for storage"
    },
    {
      id: "edge_case_year",
      name: "Edge Case Year",
      description: "Tests year boundary handling",
      initialValue: "2544-01-01", // Buddhist year 2544 = Gregorian 2001
      expectedBehavior: "Should display as 01/01/2001 in input field"
    }
  ]

  // State for each test scenario
  const [dates, setDates] = useState<Record<string, string>>(() => {
    const initialDates: Record<string, string> = {}
    testScenarios.forEach(scenario => {
      initialDates[scenario.id] = scenario.initialValue
    })
    return initialDates
  })

  const [submittedDates, setSubmittedDates] = useState<Record<string, string>>({})
  const [testResults, setTestResults] = useState<Record<string, any>>({})

  // Handle date change for a specific scenario
  const handleDateChange = (scenarioId: string, newDate: string) => {
    setDates(prev => ({
      ...prev,
      [scenarioId]: newDate
    }))

    // Real-time analysis of the date
    analyzeDate(scenarioId, newDate)
  }

  // Analyze date for debugging and testing
  const analyzeDate = (scenarioId: string, dateValue: string) => {
    if (!dateValue) {
      setTestResults(prev => ({
        ...prev,
        [scenarioId]: null
      }))
      return
    }

    try {
      const dateObj = new Date(dateValue)
      const year = dateObj.getFullYear()
      const isBuddhistEra = year > 2500
      const gregorianYear = isBuddhistEra ? year - 543 : year
      const buddhistYear = isBuddhistEra ? year : year + 543

      const analysis = {
        originalValue: dateValue,
        parsedYear: year,
        isBuddhistEra,
        gregorianYear,
        buddhistYear,
        gregorianDate: `${dateObj.getDate().toString().padStart(2, '0')}/${(dateObj.getMonth() + 1).toString().padStart(2, '0')}/${gregorianYear}`,
        buddhistDate: `${dateObj.getDate().toString().padStart(2, '0')}/${(dateObj.getMonth() + 1).toString().padStart(2, '0')}/${buddhistYear}`,
        isoString: dateObj.toISOString(),
        isValid: !isNaN(dateObj.getTime())
      }

      setTestResults(prev => ({
        ...prev,
        [scenarioId]: analysis
      }))

      console.log(`=== REAL-TIME ANALYSIS: ${scenarioId} ===`, analysis)
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [scenarioId]: { error: error.message }
      }))
    }
  }

  // Initialize analysis for pre-loaded dates
  useEffect(() => {
    testScenarios.forEach(scenario => {
      if (scenario.initialValue) {
        analyzeDate(scenario.id, scenario.initialValue)
      }
    })
  }, [])

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setSubmittedDates({ ...dates })

    console.log("=== COMPREHENSIVE DATE PICKER TEST SUBMISSION ===")
    testScenarios.forEach(scenario => {
      const dateValue = dates[scenario.id]
      console.log(`\n${scenario.name}:`)
      console.log(`  Value: ${dateValue}`)
      console.log(`  Analysis:`, testResults[scenario.id])
    })
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">ThaiDatePickerWrapper Comprehensive Test</h1>
        <p className="text-gray-600">
          Test Buddhist Era (BE) and Anno Domini (AD) date handling for both calendar selection and manual typing
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Test Scenarios */}
        <div className="grid gap-6">
          {testScenarios.map((scenario) => (
            <Card key={scenario.id} className="w-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{scenario.name}</CardTitle>
                  <Badge variant={dates[scenario.id] ? "default" : "secondary"}>
                    {dates[scenario.id] ? "Has Value" : "Empty"}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{scenario.description}</p>
                <p className="text-xs text-blue-600 font-medium">{scenario.expectedBehavior}</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <ThaiDatePickerWrapper
                  id={scenario.id}
                  label={`${scenario.name} Date Input`}
                  value={dates[scenario.id]}
                  onChange={(newDate) => handleDateChange(scenario.id, newDate)}
                  placeholder="dd/mm/yyyy"
                />

                {/* Real-time Value Display */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm mb-2">Current State:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="font-medium">Raw Value:</span>
                      <code className="ml-1 bg-white px-1 rounded">{dates[scenario.id] || "empty"}</code>
                    </div>
                    {testResults[scenario.id] && !testResults[scenario.id].error && (
                      <>
                        <div>
                          <span className="font-medium">Year Type:</span>
                          <Badge variant={testResults[scenario.id].isBuddhistEra ? "default" : "outline"} className="ml-1 text-xs">
                            {testResults[scenario.id].isBuddhistEra ? "Buddhist Era" : "Gregorian"}
                          </Badge>
                        </div>
                        <div>
                          <span className="font-medium">Gregorian Display:</span>
                          <code className="ml-1 bg-white px-1 rounded">{testResults[scenario.id].gregorianDate}</code>
                        </div>
                        <div>
                          <span className="font-medium">Buddhist Display:</span>
                          <code className="ml-1 bg-white px-1 rounded">{testResults[scenario.id].buddhistDate}</code>
                        </div>
                        <div>
                          <span className="font-medium">Storage Year:</span>
                          <code className="ml-1 bg-white px-1 rounded">{testResults[scenario.id].parsedYear}</code>
                        </div>
                        <div>
                          <span className="font-medium">ISO String:</span>
                          <code className="ml-1 bg-white px-1 rounded text-xs">{testResults[scenario.id].isoString}</code>
                        </div>
                      </>
                    )}
                    {testResults[scenario.id]?.error && (
                      <div className="col-span-2">
                        <span className="font-medium text-red-600">Error:</span>
                        <code className="ml-1 bg-red-50 px-1 rounded text-red-600">{testResults[scenario.id].error}</code>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium text-sm mb-1">Manual Typing Test:</h4>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Try typing: <code>15/06/2024</code> (Gregorian year) - should convert to Buddhist Era for storage</li>
                <li>• Try typing: <code>01/01/2567</code> (Buddhist year) - should be handled correctly</li>
                <li>• Test auto-formatting as you type (slashes should appear automatically)</li>
                <li>• Test backspace behavior at slash positions</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-sm mb-1">Calendar Selection Test:</h4>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Click calendar icon and select different dates</li>
                <li>• Verify that selected dates display correctly in Gregorian format</li>
                <li>• Check that storage value uses Buddhist Era format</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-sm mb-1">Expected Behavior:</h4>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Display: Always show Gregorian years (e.g., 2024) in the input field</li>
                <li>• Storage: Always store Buddhist Era years (e.g., 2567) in the database format</li>
                <li>• Conversion: Automatic conversion between BE and AD as needed</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Button type="submit" className="w-full">Submit All Dates for Final Analysis</Button>

        {/* Submission Results */}
        {Object.keys(submittedDates).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Submission Results</CardTitle>
              <p className="text-sm text-gray-600">Final values that would be sent to the backend</p>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {testScenarios.map((scenario) => (
                  <div key={scenario.id} className="border rounded-md p-3">
                    <h4 className="font-medium text-sm mb-2">{scenario.name}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="font-medium">Submitted Value:</span>
                        <code className="ml-1 bg-gray-100 px-1 rounded">
                          {submittedDates[scenario.id] || "empty"}
                        </code>
                      </div>
                      {submittedDates[scenario.id] && testResults[scenario.id] && !testResults[scenario.id].error && (
                        <>
                          <div>
                            <span className="font-medium">Year Format:</span>
                            <Badge variant={testResults[scenario.id].isBuddhistEra ? "default" : "outline"} className="ml-1 text-xs">
                              {testResults[scenario.id].isBuddhistEra ? "Buddhist Era" : "Gregorian"}
                            </Badge>
                          </div>
                          <div className="col-span-2">
                            <span className="font-medium">Backend Storage:</span>
                            <code className="ml-1 bg-blue-50 px-1 rounded">
                              {submittedDates[scenario.id]} (Year: {testResults[scenario.id].parsedYear})
                            </code>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </form>
    </div>
  )
}
