"use client"

import type { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal, Edit, Eye, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { CarComplete } from "@/types/models"

export const createColumns = (
  onEdit: (car: CarComplete) => void,
  onView: (car: CarComplete) => void,
  onDelete: (car: CarComplete) => void,
): ColumnDef<CarComplete>[] => [
    // Default visible columns
    {
      accessorKey: "stockInfo.index_number",
      id: "stockInfo_index_number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            ลำดับ
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.stockInfo.index_number}</div>,
      size: 100,
      filterFn: "includesString",
    },
    {
      accessorKey: "buyin.purchase_date",
      id: "buyin_purchase_date",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            วันที่ซื้อ
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.purchase_date}</div>,
      size: 100,
    },
    {
      accessorKey: "buyin.brand",
      id: "buyin_brand",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            ยี่ห้อ
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.brand}</div>,
      size: 90,
      filterFn: "includesString",
    },
    {
      accessorKey: "buyin.model",
      id: "buyin_model",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            รุ่น
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.model}</div>,
      size: 90,
      filterFn: "includesString",
    },
    {
      accessorKey: "buyin.tank_number",
      id: "buyin_tank_number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            เลขตัวถัง
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.tank_number}</div>,
      size: 100,
      filterFn: "includesString",
    },
    {
      accessorKey: "buyin.engine_number",
      id: "buyin_engine_number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            เลขเครื่องยนต์
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.engine_number}</div>,
      size: 110,
      filterFn: "includesString",
    },
    {
      accessorKey: "stockInfo.old_license_plate",
      id: "stockInfo_old_license_plate",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            ทะเบียน
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.stockInfo.old_license_plate}</div>,
      size: 100,
      filterFn: "includesString",
    },
    {
      accessorKey: "buyin.parking_location",
      id: "buyin_parking_location",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            สถานที่
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.original.buyin?.parking_location}</div>,
      size: 90,
      filterFn: "includesString",
    },
    {
      accessorKey: "stockInfo.car_status",
      id: "stockInfo_car_status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-white hover:text-white hover:bg-primary w-full justify-start p-1 h-auto text-xs thaifont"
          >
            สถานะ
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const status = row.original.stockInfo.car_status

        // Thai translations for status badges
        const statusLabels: Record<string, string> = {
          available: "พร้อมขาย",
          sold: "ขายแล้ว",
          in_repair: "กำลังซ่อม",
          repair: "กำลังซ่อม",
          finance_request: "ขอไฟแนนซ์",
          finance_done: "ไฟแนนซ์เสร็จ",
          reserved: "จอง",
          purchase: "ซื้อเข้า",
          transfer: "ขนส่ง",
        }

        // Status badge styling with enhanced visual appearance
        const statusStyles: Record<string, string> = {
          available: "bg-green-100 text-green-800 border-green-200 hover:bg-green-200",
          sold: "bg-red-100 text-red-800 border-red-200 hover:bg-red-200",
          in_repair: "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200",
          repair: "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200",
          finance_request: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",
          finance_done: "bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200",
          reserved: "bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200",
          purchase: "bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200",
          transfer: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",
        }

        return (
          <Badge
            variant="outline"
            className={`text-xs px-2 py-0.5 thaifont font-medium rounded-full shadow-sm ${statusStyles[status] || "bg-gray-100 text-gray-800 border-gray-200"}`}
          >
            {statusLabels[status] || status.replace(/_/g, " ")}
          </Badge>
        )
      },
      size: 90,
      filterFn: "equals",
    },

    // Hidden columns - StockInfo
    {
      accessorKey: "stockInfo.car_id",
      id: "stockInfo_car_id",
      header: "รหัสรถ",
      cell: ({ row }) => <div>{row.original.stockInfo.car_id}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.created_at",
      id: "stockInfo_created_at",
      header: "วันที่สร้าง",
      cell: ({ row }) => <div>{row.original.stockInfo.created_at}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.updated_at",
      id: "stockInfo_updated_at",
      header: "วันที่อัปเดต",
      cell: ({ row }) => <div>{row.original.stockInfo.updated_at}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.is_auction_car",
      id: "stockInfo_is_auction_car",
      header: "รถประมูล",
      cell: ({ row }) => <div>{row.original.stockInfo.is_auction_car ? "Yes" : "No"}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.new_license_plate",
      id: "stockInfo_new_license_plate",
      header: "ทะเบียนใหม่",
      cell: ({ row }) => <div>{row.original.stockInfo.new_license_plate}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.registration_date",
      id: "stockInfo_registration_date",
      header: "วันจดทะเบียน",
      cell: ({ row }) => <div>{row.original.stockInfo.registration_date}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.registration_book_received_date",
      id: "stockInfo_registration_book_received_date",
      header: "วันที่รับเล่มทะเบียน",
      cell: ({ row }) => <div>{row.original.stockInfo.registration_book_received_date}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.total_investment",
      id: "stockInfo_total_investment",
      header: "ทุนรวมทั้งหมด",
      cell: ({ row }) => <div>{row.original.stockInfo.total_investment}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.listed_price",
      id: "stockInfo_listed_price",
      header: "ราคาตั้งขาย",
      cell: ({ row }) => <div>{row.original.stockInfo.listed_price}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "stockInfo.notes",
      id: "stockInfo_notes",
      header: "หมายเหตุ",
      cell: ({ row }) => <div>{row.original.stockInfo.notes}</div>,
      size: 150,
      enableHiding: true,
      meta: { hidden: true },
    },

    // Hidden columns - BuyIn
    {
      accessorKey: "buyin.color",
      id: "buyin_color",
      header: "สี",
      cell: ({ row }) => <div>{row.original.buyin?.color}</div>,
      size: 80,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.year",
      id: "buyin_year",
      header: "ปี",
      cell: ({ row }) => <div>{row.original.buyin?.year}</div>,
      size: 70,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.vat_percent",
      id: "buyin_vat_percent",
      header: "VAT %",
      cell: ({ row }) => <div>{row.original.buyin?.vat_percent}%</div>,
      size: 70,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.purchase_price",
      id: "buyin_purchase_price",
      header: "ราคาซื้อเข้า",
      cell: ({ row }) => <div>{row.original.buyin?.purchase_price}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.purchase_vat_percent",
      id: "buyin_purchase_vat_percent",
      header: "VAT(%)ซื้อ",
      cell: ({ row }) => <div>{row.original.buyin?.purchase_vat_percent}%</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.operation_cost_incl_vat",
      id: "buyin_operation_cost_incl_vat",
      header: "ค่าดำเนินการรวม VAT",
      cell: ({ row }) => <div>{row.original.buyin?.operation_cost_incl_vat}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.transport_1_auction_lot",
      id: "buyin_transport_1_auction_lot",
      header: "ค่าขนส่ง 1 ลานประมูล",
      cell: ({ row }) => <div>{row.original.buyin?.transport_1_auction_lot}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.transport_2_personal_payment",
      id: "buyin_transport_2_personal_payment",
      header: "ค่าขนส่ง 2 บุคคล",
      cell: ({ row }) => <div>{row.original.buyin?.transport_2_personal_payment}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.transport_3_tl_payment",
      id: "buyin_transport_3_tl_payment",
      header: "ค่าขนส่ง 3 TL",
      cell: ({ row }) => <div>{row.original.buyin?.transport_3_tl_payment}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.initial_check",
      id: "buyin_initial_check",
      header: "ตรวจสอบเบื้องต้น",
      cell: ({ row }) => <div>{row.original.buyin?.initial_check}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.tax_insurance_cost_zero",
      id: "buyin_tax_insurance_cost_zero",
      header: "ภาษี-พรบ.+คชจ(0%)",
      cell: ({ row }) => <div>{row.original.buyin?.tax_insurance_cost_zero}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.other_costs_seven",
      id: "buyin_other_costs_seven",
      header: "คชจ.อื่นๆ(7%)",
      cell: ({ row }) => <div>{row.original.buyin?.other_costs_seven}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.five_three_tax_percentage",
      id: "buyin_five_three_tax_percentage",
      header: "ภงด.53",
      cell: ({ row }) => <div>{row.original.buyin?.five_three_tax_percentage}%</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.total_purchase_cost",
      id: "buyin_total_purchase_cost",
      header: "รวมซื้อเข้า",
      cell: ({ row }) => <div>{row.original.buyin?.total_purchase_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.auction_name",
      id: "buyin_auction_name",
      header: "ชื่อลานประมูล",
      cell: ({ row }) => <div>{row.original.buyin?.auction_name}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.auction_provinced",
      id: "buyin_auction_provinced",
      header: "จังหวัดที่ประมูล",
      cell: ({ row }) => <div>{row.original.buyin?.auction_provinced}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.auction_order",
      id: "buyin_auction_order",
      header: "ลำดับประมูล",
      cell: ({ row }) => <div>{row.original.buyin?.auction_order}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.auction_checker",
      id: "buyin_auction_checker",
      header: "ผู้ตรวจสอบประมูล",
      cell: ({ row }) => <div>{row.original.buyin?.auction_checker}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.transport_personal",
      id: "buyin_transport_personal",
      header: "ขนส่งบุคคล",
      cell: ({ row }) => <div>{row.original.buyin?.transport_personal}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.transport_company",
      id: "buyin_transport_company",
      header: "บริษัทขนส่ง",
      cell: ({ row }) => <div>{row.original.buyin?.transport_company}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.book_deposit",
      id: "buyin_book_deposit",
      header: "มัดจำเล่ม",
      cell: ({ row }) => <div>{row.original.buyin?.book_deposit}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "buyin.type_of_transport",
      id: "buyin_type_of_transport",
      header: "ประเภทการขนส่ง",
      cell: ({ row }) => <div>{row.original.buyin?.type_of_transport}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    // Hidden columns - Repair
    {
      accessorKey: "repair.repainting_cost",
      id: "repair_repainting_cost",
      header: "ทำสี",
      cell: ({ row }) => <div>{row.original.repair?.repainting_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "repair.engine_repair_cost",
      id: "repair_engine_repair_cost",
      header: "ซ่อมเครื่องยนต์",
      cell: ({ row }) => <div>{row.original.repair?.engine_repair_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "repair.suspension_repair_cost",
      id: "repair_suspension_repair_cost",
      header: "ซ่อมช่วงล่าง",
      cell: ({ row }) => <div>{row.original.repair?.suspension_repair_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "repair.autopart_cost",
      id: "repair_autopart_cost",
      header: "ประดับยนต์",
      cell: ({ row }) => <div>{row.original.repair?.autopart_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "repair.battery_cost",
      id: "repair_battery_cost",
      header: "แบตเตอรี่",
      cell: ({ row }) => <div>{row.original.repair?.battery_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "repair.tires_wheels_cost",
      id: "repair_tires_wheels_cost",
      header: "ยางรถยนต์/แม็ก",
      cell: ({ row }) => <div>{row.original.repair?.tires_wheels_cost}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },

    // Hidden columns - Finance
    {
      accessorKey: "finance.finance_request_date",
      id: "finance_finance_request_date",
      header: "วันที่ยื่นไฟแนนซ์",
      cell: ({ row }) => <div>{row.original.finance?.finance_request_date}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.finance_received_date",
      id: "finance_finance_received_date",
      header: "วันที่รับเงินไฟแนนซ์",
      cell: ({ row }) => <div>{row.original.finance?.finance_received_date}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_tax_invoice_date",
      id: "finance_car_tax_invoice_date",
      header: "วันออกภาษี-ขาย-Car",
      cell: ({ row }) => <div>{row.original.finance?.car_tax_invoice_date}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.commission_tax_invoice_date",
      id: "finance_commission_tax_invoice_date",
      header: "วันออกภาษี-ขาย-Com",
      cell: ({ row }) => <div>{row.original.finance?.commission_tax_invoice_date}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_tax_invoice_number",
      id: "finance_car_tax_invoice_number",
      header: "เลขที่/เล่มที่ออกภาษี-Car",
      cell: ({ row }) => <div>{row.original.finance?.car_tax_invoice_number}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.commission_tax_invoice_number",
      id: "finance_commission_tax_invoice_number",
      header: "เล่มที่ /เลขที่ออกภาษี-Comm",
      cell: ({ row }) => <div>{row.original.finance?.commission_tax_invoice_number}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_amount",
      id: "finance_car_amount",
      header: "CAR AMOUNT",
      cell: ({ row }) => <div>{row.original.finance?.car_amount}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_vat_amount",
      id: "finance_car_vat_amount",
      header: "VAT ON CAR AMOUNT",
      cell: ({ row }) => <div>{row.original.finance?.car_vat_amount}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_commission_amount",
      id: "finance_car_commission_amount",
      header: "COMMISSION OF CAR",
      cell: ({ row }) => <div>{row.original.finance?.car_commission_amount}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.input_vat_commission",
      id: "finance_input_vat_commission",
      header: "Input-VAT-comm.",
      cell: ({ row }) => <div>{row.original.finance?.input_vat_commission}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.withholding_tax",
      id: "finance_withholding_tax",
      header: "WITHOLDING TAX",
      cell: ({ row }) => <div>{row.original.finance?.withholding_tax}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.bank",
      id: "finance_bank",
      header: "ธนาคาร",
      cell: ({ row }) => <div>{row.original.finance?.bank}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.salesperson",
      id: "finance_salesperson",
      header: "พนักงานขาย",
      cell: ({ row }) => <div>{row.original.finance?.salesperson}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.marketing_person",
      id: "finance_marketing_person",
      header: "มาร์เก็ตติ้ง",
      cell: ({ row }) => <div>{row.original.finance?.marketing_person}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.promotion_customer",
      id: "finance_promotion_customer",
      header: "Promotion-ให้ลค.",
      cell: ({ row }) => <div>{row.original.finance?.promotion_customer}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.bonus_insurance_car_life_engine",
      id: "finance_bonus_insurance_car_life_engine",
      header: "ประกันแถม(รถ+ชีวิต+เครื่อง)",
      cell: ({ row }) => <div>{row.original.finance?.bonus_insurance_car_life_engine}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.customer_name",
      id: "finance_customer_name",
      header: "ลูกค้า",
      cell: ({ row }) => <div>{row.original.finance?.customer_name}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.customer_address_or_advance_payment",
      id: "finance_customer_address_or_advance_payment",
      header: "ที่อยู่/ค่างวดล่วงหน้า",
      cell: ({ row }) => <div>{row.original.finance?.customer_address_or_advance_payment}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.down_payment",
      id: "finance_down_payment",
      header: "เงินดาวน์(บาท)",
      cell: ({ row }) => <div>{row.original.finance?.down_payment}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.loan_protection_insurance",
      id: "finance_loan_protection_insurance",
      header: "ประกันคุ้มครองสินเชื่อ",
      cell: ({ row }) => <div>{row.original.finance?.loan_protection_insurance}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.accident_insurance",
      id: "finance_accident_insurance",
      header: "ประกันอุบัติเหตุ",
      cell: ({ row }) => <div>{row.original.finance?.accident_insurance}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.car_insurance",
      id: "finance_car_insurance",
      header: "ประกันรถยนต์",
      cell: ({ row }) => <div>{row.original.finance?.car_insurance}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "finance.bank_documents",
      id: "finance_bank_documents",
      header: "จัดเอกสาร-Bank",
      cell: ({ row }) => <div>{row.original.finance?.bank_documents}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },

    // Hidden columns - Sellout
    {
      accessorKey: "sellout.sale_date",
      id: "sellout_sale_date",
      header: "วันที่ขาย",
      cell: ({ row }) => <div>{row.original.sellout?.sale_date}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.owner_name",
      id: "sellout_owner_name",
      header: "เจ้าของ",
      cell: ({ row }) => <div>{row.original.sellout?.owner_name}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.actual_selling_price",
      id: "sellout_actual_selling_price",
      header: "ราคาขายจริง",
      cell: ({ row }) => <div>{row.original.sellout?.actual_selling_price}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.sales_channel",
      id: "sellout_sales_channel",
      header: "ช่องทางการขาย",
      cell: ({ row }) => <div>{row.original.sellout?.sales_channel}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.customer_address",
      id: "sellout_customer_address",
      header: "ที่อยู่ลูกค้า",
      cell: ({ row }) => <div>{row.original.sellout?.customer_address}</div>,
      size: 200,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.commission_s",
      id: "sellout_commission_s",
      header: "Comm.-S",
      cell: ({ row }) => <div>{row.original.sellout?.commission_s}</div>,
      size: 100,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.commission_agent",
      id: "sellout_commission_agent",
      header: "Comm-นายหน้า",
      cell: ({ row }) => <div>{row.original.sellout?.commission_agent}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },
    {
      accessorKey: "sellout.commission_manager",
      id: "sellout_commission_manager",
      header: "Comm-ผจก",
      cell: ({ row }) => <div>{row.original.sellout?.commission_manager}</div>,
      size: 120,
      enableHiding: true,
      meta: { hidden: true },
    },

    // Actions column (always visible)
    {
      id: "actions",
      cell: ({ row }) => {
        const car = row.original

        return (
          <div className="flex justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-6 w-6 p-0">
                  <span className="sr-only">เปิดเมนู</span>
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="text-xs">
                <DropdownMenuLabel className="text-xs thaifont">การดำเนินการ</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => onEdit(car)}
                  className="text-xs cursor-pointer flex items-center gap-2 thaifont"
                >
                  <Edit className="h-3 w-3" /> แก้ไขรถยนต์
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onView(car)}
                  className="text-xs cursor-pointer flex items-center gap-2 thaifont"
                >
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(car)}
                  className="text-xs cursor-pointer flex items-center gap-2 text-red-500 hover:text-red-500 thaifont"
                >
                  <Trash2 className="h-3 w-3" /> ลบรถยนต์
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
      size: 50,
    },
  ]
