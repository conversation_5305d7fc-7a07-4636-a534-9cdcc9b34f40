"use client"

import { useState, useEffect } from "react"
import { X, <PERSON>, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Check } from "lucide-react"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"
import dayjs from "dayjs"
import "dayjs/locale/th"
import buddhistEra from "dayjs/plugin/buddhistEra"

dayjs.extend(buddhistEra)
dayjs.locale("th")

// Define field types
type FieldType = "text" | "number" | "date"

export interface FilterOption {
  field: string
  operator: string
  value: string
  fieldType: FieldType
}

interface AdvancedFilterProps {
  onFilterApply: (filters: FilterOption[]) => void
}

export function AdvancedFilter({ onFilterApply }: AdvancedFilterProps) {
  const [filters, setFilters] = useState<FilterOption[]>([])
  const [field, setField] = useState("")
  const [operator, setOperator] = useState("")
  const [value, setValue] = useState("")
  const [openFieldPopover, setOpenFieldPopover] = useState(false)
  const [fieldType, setFieldType] = useState<FieldType>("text")
  const [dateRangeStart, setDateRangeStart] = useState("")
  const [dateRangeEnd, setDateRangeEnd] = useState("")

  // Field options with Thai labels, their corresponding database fields, and their types
  const fieldOptions = [
    { value: "stockInfo.index_number", label: "ลำดับ", type: "text" as FieldType },
    { value: "buyin.brand", label: "ยี่ห้อ", type: "text" as FieldType },
    { value: "buyin.model", label: "รุ่น", type: "text" as FieldType },
    { value: "buyin.year", label: "ปี", type: "number" as FieldType },
    { value: "buyin.color", label: "สี", type: "text" as FieldType },
    { value: "buyin.purchase_price", label: "ราคาซื้อเข้า", type: "number" as FieldType },
    { value: "stockInfo.listed_price", label: "ราคาตั้งขาย", type: "number" as FieldType },
    { value: "buyin.tank_number", label: "เลขตัวถัง", type: "text" as FieldType },
    { value: "buyin.engine_number", label: "เลขเครื่องยนต์", type: "text" as FieldType },
    { value: "stockInfo.car_status", label: "สถานะรถ", type: "text" as FieldType },
    { value: "stockInfo.old_license_plate", label: "ทะเบียนเดิม", type: "text" as FieldType },
    { value: "stockInfo.new_license_plate", label: "ทะเบียนใหม่", type: "text" as FieldType },
    { value: "buyin.parking_location", label: "สถานที่จอด", type: "text" as FieldType },
    { value: "buyin.auction_name", label: "ซื่อลานประมูล", type: "text" as FieldType },
    { value: "buyin.auction_provinced", label: "จังหวัด", type: "text" as FieldType },
    { value: "buyin.auction_order", label: "ลำดับรถ", type: "text" as FieldType },
    { value: "buyin.auction_checker", label: "QC", type: "text" as FieldType },
    { value: "buyin.auction_transporter", label: "ขนย้าย", type: "text" as FieldType },
    { value: "buyin.purchase_date", label: "วันที่ซื้อ", type: "date" as FieldType },
    { value: "stockInfo.registration_book_received_date", label: "วันที่รับเล่มทะเบียน", type: "date" as FieldType },
    { value: "sellout.sale_date", label: "วันที่ขาย", type: "date" as FieldType },
    { value: "finance.finance_received_date", label: "วันที่รับเงินFinance", type: "date" as FieldType },
    { value: "finance.car_tax_invoice_date", label: "วันออกภาษี-ขาย-Car", type: "date" as FieldType },
    { value: "finance.car_tax_invoice_number", label: "เล่มที่ /เลขที่ออกภาษี-Car", type: "text" as FieldType },
    { value: "finance.car_amount", label: "CAR AMOUNT", type: "number" as FieldType },
    { value: "finance.car_vat_amount", label: "VAT ON CAR AMOUNT", type: "number" as FieldType },
    { value: "finance.commission_tax_invoice_date", label: "วันออกภาษี-ขาย-Com", type: "date" as FieldType },
    { value: "finance.commission_tax_invoice_number", label: "เล่มที่ /เลขที่ออกภาษี-Comm", type: "text" as FieldType },
    { value: "finance.car_commission_amount", label: "COMMISSION OF CAR", type: "number" as FieldType },
    { value: "finance.input_vat_commission", label: "Input-VAT-comm.", type: "number" as FieldType },
    { value: "finance.withholding_tax", label: "WITHOLDING TAX", type: "number" as FieldType },
    { value: "finance.salesperson", label: "Sale", type: "text" as FieldType },
    { value: "finance.bank", label: "BANK", type: "text" as FieldType },
    { value: "finance.marketing_person", label: "MKT.", type: "text" as FieldType },
    { value: "sellout.owner_name", label: "เจ้าของ", type: "text" as FieldType },
    { value: "buyin.transport_2_personal_payment", label: "TRAN 2 -บุคคล", type: "number" as FieldType },
    { value: "buyin.transport_3_tl_payment", label: "TRAN 3-TL", type: "number" as FieldType },
    { value: "sellout.customer_address", label: "ที่อยู่", type: "text" as FieldType },
    { value: "buyin.qc1_auction_lot", label: "QC-1-ลานประมูล", type: "number" as FieldType },
    { value: "buyin.ems_registration_qc3", label: "EMS-ทะเบียน/QC-3", type: "number" as FieldType },
    { value: "buyin.registration_fee", label: "ค่าจ้างงานทะเบียน", type: "number" as FieldType },
    { value: "buyin.vat_percent", label: "VAT(%)", type: "number" as FieldType },
    { value: "buyin.purchase_vat_percent", label: "VAT(%)ซื้อ", type: "number" as FieldType },
    { value: "buyin.operation_cost_incl_vat", label: "ค่าดำเนินการรวมVAT", type: "number" as FieldType },
    { value: "buyin.transport_1_auction_lot", label: "TRAN-1-ขนย้าย-ลานประมูล", type: "number" as FieldType },
    { value: "buyin.initial_check", label: "เช็คต้น", type: "number" as FieldType },
    { value: "buyin.tax_insurance_cost_zero", label: "ภาษี-พรบ.+คชจ(0%)", type: "number" as FieldType },
    { value: "buyin.other_costs_seven", label: "คชจ.อื่นๆ(7%)", type: "number" as FieldType },
    { value: "buyin.five_three_tax_percentage", label: "ภงด.53", type: "number" as FieldType },
    { value: "buyin.total_purchase_cost", label: "รวมซื้อเข้า", type: "number" as FieldType },
    { value: "repair.repainting_cost", label: "ทำสี", type: "number" as FieldType },
    { value: "stockInfo.total_investment", label: "ทุนรวมทั้งหมด", type: "number" as FieldType },
    { value: "sellout.actual_selling_price", label: "ราคาขายจริง", type: "number" as FieldType },
    { value: "buyin.transport_personal", label: "TR-บุคคล", type: "text" as FieldType },
    { value: "repair.engine_repair_cost", label: "ซ่อมเครื่องยนต์", type: "number" as FieldType },
    { value: "repair.suspension_repair_cost", label: "ซ่อมช่วงล่าง", type: "number" as FieldType },
    { value: "repair.autopart_cost", label: "ประดับยนต์", type: "number" as FieldType },
    { value: "repair.battery_cost", label: "Battery", type: "number" as FieldType },
    { value: "repair.tires_wheels_cost", label: "ยางรถยนต์/แม็ก", type: "number" as FieldType },
    { value: "sellout.commission_s", label: "Comm.-S", type: "number" as FieldType },
    { value: "sellout.commission_agent", label: "Comm-นายหน้า", type: "number" as FieldType },
    { value: "sellout.commission_manager", label: "Comm-ผจก", type: "number" as FieldType },
    { value: "finance.promotion_customer", label: "Promotion-ให้ลค.", type: "number" as FieldType },
    { value: "finance.bonus_insurance_car_life_engine", label: "ประกันแถม(รถ+ชีวิต+เครื่อง)", type: "number" as FieldType },
    { value: "finance.customer_name", label: "ลูกค้า", type: "text" as FieldType },
    { value: "finance.customer_address_or_advance_payment", label: "ที่อยู่/ค่างวดล่วงหน้า", type: "text" as FieldType },
    { value: "buyin.book_deposit", label: "มัดจำเล่ม", type: "number" as FieldType },
    { value: "finance.down_payment", label: "เงินดาวน์(บาท)", type: "number" as FieldType },
    { value: "sellout.sales_channel", label: "ช่องทาง", type: "text" as FieldType },
    { value: "stockInfo.notes", label: "*หมายเหตุ", type: "text" as FieldType },
    { value: "finance.loan_protection_insurance", label: "ประกันคุ้มครองสินเชื่อ", type: "number" as FieldType },
    { value: "finance.accident_insurance", label: "ประกันอุบัติเหตุ", type: "number" as FieldType },
    { value: "finance.car_insurance", label: "ประกันรถยนต์", type: "number" as FieldType },
    { value: "finance.bank_documents", label: "จัดเอกสาร-Bank", type: "number" as FieldType },
  ]

  // Get operator options based on field type
  const getOperatorOptions = (type: FieldType) => {
    switch (type) {
      case "text":
        return [
          { value: "contains", label: "ประกอบด้วย" },
          { value: "empty", label: "ไม่มีข้อมูล" },
        ]
      case "number":
        return [
          { value: "equals", label: "เท่ากับ" },
          { value: "greater_than_equal", label: "มากกว่าเท่ากับ" },
          { value: "less_than_equal", label: "น้อยกว่าเท่ากับ" },
          { value: "empty", label: "ไม่มีข้อมูล" },
        ]
      case "date":
        // We'll handle date differently with a range picker
        return [
          { value: "range", label: "ช่วงวันที่" },
          { value: "empty", label: "ไม่มีข้อมูล" },
        ]
      default:
        return []
    }
  }

  // Update operator options when field changes
  useEffect(() => {
    if (field) {
      const selectedField = fieldOptions.find((option) => option.value === field)
      if (selectedField) {
        setFieldType(selectedField.type)
        // Set default operator based on field type
        const operatorOptions = getOperatorOptions(selectedField.type)
        if (operatorOptions.length > 0) {
          setOperator(operatorOptions[0].value)
        }
      }
    }
  }, [field])

  // Get input type based on field type
  const getInputType = (type: FieldType) => {
    switch (type) {
      case "number":
        return "number"
      case "date":
        return "date"
      default:
        return "text"
    }
  }

  // Check if value input should be disabled (for empty/not_empty operators)
  const isValueInputDisabled = () => {
    return operator === "empty" || operator === "not_empty"
  }

  // Reset all form fields to their default state
  const resetFormFields = () => {
    setField("")
    setOperator("")
    setValue("")
    setFieldType("text")
    setDateRangeStart("")
    setDateRangeEnd("")
  }

  const addFilter = () => {
    if (field) {
      const selectedField = fieldOptions.find((option) => option.value === field)
      if (!selectedField) return

      // For empty operator, we don't need a value
      if (operator === "empty") {
        const newFilter: FilterOption = {
          field,
          operator,
          value: "",
          fieldType: selectedField.type,
        }

        const newFilters = [...filters, newFilter]
        setFilters(newFilters)
        onFilterApply(newFilters)
        resetFormFields()
        return
      }

      // For date range, we need at least one date (start or end)
      if (fieldType === "date" && operator === "range") {
        if (!dateRangeStart && !dateRangeEnd) return

        const newFilter: FilterOption = {
          field,
          operator: "range",
          value: JSON.stringify({ start: dateRangeStart, end: dateRangeEnd }),
          fieldType: "date",
        }

        const newFilters = [...filters, newFilter]
        setFilters(newFilters)
        onFilterApply(newFilters)
        resetFormFields()
        return
      }

      // For other operators, we need a value
      if (!value) return

      const newFilter: FilterOption = {
        field,
        operator,
        value,
        fieldType: selectedField.type,
      }

      const newFilters = [...filters, newFilter]
      setFilters(newFilters)
      onFilterApply(newFilters)
      resetFormFields()
    }
  }

  const removeFilter = (index: number) => {
    const newFilters = filters.filter((_, i) => i !== index)
    setFilters(newFilters)
    onFilterApply(newFilters)
  }

  const clearAllFilters = () => {
    setFilters([])
    onFilterApply([])
    resetFormFields()
  }

  // Format date for display using Thai locale (Buddhist calendar)
  const formatThaiDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString)
      const year = date.getFullYear()

      // For dates already in Buddhist Era (year > 2500), we need to subtract 543 years
      // to display the Gregorian year
      if (year > 2500) {
        // Use custom formatting to display the Gregorian year
        const day = date.getDate()
        const month = dayjs(dateString).format("MMM") // Get month name in Thai
        const gregorianYear = year - 543
        return `${day} ${month} ${gregorianYear}`
      } else {
        // For dates in Gregorian format, use standard formatting
        return dayjs(dateString).format("D MMM YYYY")
      }
    } catch (e) {
      return dateString
    }
  }

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
        <div>
          <Label htmlFor="field" className="text-xs font-medium thaifont">
            ฟิลด์
          </Label>
          <Popover open={openFieldPopover} onOpenChange={setOpenFieldPopover}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openFieldPopover}
                className="w-full h-7 justify-between text-xs thaifont"
              >
                {field ? fieldOptions.find((option) => option.value === field)?.label : "เลือกฟิลด์"}
                <Search className="ml-2 h-3 w-3 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0">
              <Command>
                <CommandInput placeholder="ค้นหาฟิลด์..." className="h-7 text-xs thaifont" />
                <CommandList>
                  <CommandEmpty className="text-xs thaifont">ไม่พบฟิลด์</CommandEmpty>
                  <CommandGroup className="max-h-[200px] overflow-auto">
                    {fieldOptions.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.label} // Use Thai label for search value
                        onSelect={() => {
                          setField(option.value)
                          setOpenFieldPopover(false)
                        }}
                        className="text-xs thaifont"
                      >
                        <Check className={cn("mr-2 h-3 w-3", field === option.value ? "opacity-100" : "opacity-0")} />
                        {option.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label htmlFor="operator" className="text-xs font-medium thaifont">
            ตัวดำเนินการ
          </Label>
          <Select value={operator} onValueChange={setOperator}>
            <SelectTrigger id="operator" className="w-full h-7 text-xs thaifont">
              <SelectValue placeholder="เลือกตัวดำเนินการ" />
            </SelectTrigger>
            <SelectContent>
              {getOperatorOptions(fieldType).map((option) => (
                <SelectItem key={option.value} value={option.value} className="text-xs thaifont">
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="value" className="text-xs font-medium thaifont">
            ค่า
          </Label>
          {fieldType === "date" && operator === "range" ? (
            <div className="thai-date-range-container">
              <div className="w-full">
                <ThaiDatePickerWrapper
                  value={dateRangeStart}
                  onChange={(date) => {
                    console.log('Start date changed:', date);
                    setDateRangeStart(date);
                  }}
                  placeholder="จาก"
                  className="w-full"
                  showClearButton={false}
                />
              </div>
              <span className="date-range-separator thaifont">ถึง</span>
              <div className="w-full">
                <ThaiDatePickerWrapper
                  value={dateRangeEnd}
                  onChange={(date) => {
                    console.log('End date changed:', date);
                    setDateRangeEnd(date);
                  }}
                  placeholder="ถึง"
                  className="w-full"
                  showClearButton={false}
                />
              </div>
            </div>
          ) : (
            <Input
              id="value"
              type={getInputType(fieldType)}
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="ใส่ค่า"
              className="h-7 text-xs thaifont"
              disabled={isValueInputDisabled()}
            />
          )}
        </div>
        <div className="flex items-end gap-1">
          <Button
            onClick={addFilter}
            className="bg-primary hover:bg-primary-dark h-7 text-xs thaifont"
            disabled={
              !field ||
              (!isValueInputDisabled() &&
                !((fieldType === "date" && operator === "range" && (dateRangeStart || dateRangeEnd)) || value))
            }
          >
            ใช้งาน
          </Button>
          {filters.length > 0 && (
            <Button variant="outline" onClick={clearAllFilters} className="h-7 text-xs thaifont">
              ล้างทั้งหมด
            </Button>
          )}
        </div>
      </div>

      {filters.length > 0 && (
        <div className="flex flex-wrap gap-1 pt-1">
          {filters.map((filter, index) => {
            const fieldOption = fieldOptions.find((o) => o.value === filter.field)
            const fieldLabel = fieldOption?.label || filter.field
            const fieldType = fieldOption?.type || "text"
            const operatorLabel =
              getOperatorOptions(fieldType).find((o) => o.value === filter.operator)?.label || filter.operator

            // For empty/not_empty operators, don't show the value
            const showValue = filter.operator !== "empty" && filter.operator !== "not_empty"

            // Format the value for display based on field type
            let displayValue = filter.value
            if (showValue) {
              if (fieldType === "date") {
                if (filter.operator === "range") {
                  try {
                    const range = JSON.parse(filter.value)
                    const start = range.start ? formatThaiDate(range.start) : "ไม่ระบุ"
                    const end = range.end ? formatThaiDate(range.end) : "ไม่ระบุ"
                    displayValue = `${start} - ${end}`
                  } catch (e) {
                    displayValue = filter.value
                  }
                } else {
                  displayValue = formatThaiDate(filter.value)
                }
              }
            }

            return (
              <div key={index} className="date-filter-badge">
                {fieldLabel} {operatorLabel} {showValue ? displayValue : ""}
                <button
                  type="button"
                  className="date-filter-badge-clear"
                  onClick={() => removeFilter(index)}
                  aria-label="Remove filter"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

interface FilterContainerProps {
  showAdvancedFilter: boolean
  handleAdvancedFilterApply: (filters: FilterOption[]) => void
  clearAllFilters: () => void
  filters: FilterOption[]
}

export function FilterContainer({
  showAdvancedFilter,
  handleAdvancedFilterApply,
  clearAllFilters,
  filters,
}: FilterContainerProps) {
  return (
    <>
      {showAdvancedFilter && (
        <div className="rounded-md border p-3 bg-gray-50/80 mb-2 shadow-sm">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="text-sm font-medium text-primary-dark thaifont flex items-center">
              <Filter className="h-3.5 w-3.5 mr-1.5 text-primary" />
              ตัวกรองขั้นสูง
            </h3>
            {filters.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="h-6 text-xs thaifont text-red-500 hover:text-red-600 hover:bg-red-50 px-2"
              >
                <X className="h-3 w-3 mr-1" />
                ล้างตัวกรองทั้งหมด
              </Button>
            )}
          </div>
          <AdvancedFilter onFilterApply={handleAdvancedFilterApply} />
        </div>
      )}
    </>
  )
}
