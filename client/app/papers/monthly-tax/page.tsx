"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, ArrowRight, Calendar, FileDown, FileSpreadsheet, Search } from "lucide-react"
import Link from "next/link"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formatCurrency } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { formatTaxDate, calculatePurchaseTotals, calculatePurchaseDerived, calculateSelloutTotals, calculateSelloutDerived } from "./utils"

import {
  Purchase<PERSON>axD<PERSON>,
  SelloutTaxData,
  PurchaseTaxTotals,
  Purchase<PERSON>alculated,
  SelloutTaxTotals,
  SelloutCalculated
} from "./types"
import { MONTHLY_TAX_API } from "../../services/monthlyTaxService"

// Type for the car data from API
interface CarComplete {
  stockInfo: {
    car_id: string
    index_number: string
    car_status: string
    old_license_plate: string
  }
  buyin?: {
    purchase_date: string
    brand: string
    model: string
    color: string
    year: number
    vat_percent: number
    purchase_price: number
    purchase_vat_percent: number
    operation_cost_incl_vat: number
    transport_1_auction_lot: number
    initial_check: number
    tax_insurance_cost_zero: number
    other_costs_seven: number
    five_three_tax_percentage: number
    tank_number: string
  }
  finance?: {
    finance_received_date: string
    car_tax_invoice_date: string
    car_tax_invoice_number: string
    car_amount: number
    car_vat_amount: number
    commission_tax_invoice_date: string
    commission_tax_invoice_number: string
    car_commission_amount: number
  }
  sellout?: {
    sale_date: string
  }
}

export default function MonthlyTaxPage() {
  const [activeTab, setActiveTab] = useState("purchase")
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear() + 543) // Current year in Buddhist format
  const [isLoading, setIsLoading] = useState(false)
  const [purchaseTaxData, setPurchaseTaxData] = useState<PurchaseTaxData[]>([])
  const [selloutTaxData, setSelloutTaxData] = useState<SelloutTaxData[]>([])
  const [searchQuery, setSearchQuery] = useState("")

  // Generate month options
  const months = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ]

  // Generate year options (last 5 years) in Buddhist format
  const currentYear = new Date().getFullYear() + 543 // Convert to Buddhist year
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Fetch tax data
  const fetchTaxData = async () => {
    setIsLoading(true)
    try {
      // Get auth token from localStorage
      const authToken = localStorage.getItem('token') || '';

      // Convert Buddhist year to Gregorian year for the API
      const gregorianYear = selectedYear - 543;
      console.log("Buddhist Year:", selectedYear, "Gregorian Year:", gregorianYear);

      const response = await MONTHLY_TAX_API.GET_MONTHLY_TAX(
        selectedMonth,
        selectedYear,
        authToken
      );

      setPurchaseTaxData(response.purchaseData)
      setSelloutTaxData(response.selloutData)
    } catch (error) {
      console.error("Error fetching tax data:", error)
      // Set empty arrays on error
      setPurchaseTaxData([])
      setSelloutTaxData([])
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on initial load and when month/year changes
  useEffect(() => {
    fetchTaxData()
  }, [selectedMonth, selectedYear])

  // Debug data if needed
  // console.log("Purchase Tax Data:", purchaseTaxData)
  // Filter data based on search query
  const filteredPurchaseData = purchaseTaxData.filter(
    (item) => {
      // Safely check if properties exist and are strings before calling toLowerCase()
      const brand = typeof item.brand === 'string' ? item.brand.toLowerCase() : '';
      const model = typeof item.model === 'string' ? item.model.toLowerCase() : '';
      const licensePlate = typeof item.old_license_plate === 'string' ? item.old_license_plate.toLowerCase() : '';

      const query = searchQuery.toLowerCase();

      return brand.includes(query) ||
             model.includes(query) ||
             licensePlate.includes(query);
    }
  )

  const filteredSelloutData = selloutTaxData.filter(
    (item) => {
      // Safely check if properties exist and are strings before calling toLowerCase()
      const tankNumber = typeof item.tank_number === 'string' ? item.tank_number.toLowerCase() : '';
      const invoiceNumber = typeof item.car_tax_invoice_number === 'string' ? item.car_tax_invoice_number.toLowerCase() : '';

      const query = searchQuery.toLowerCase();

      return tankNumber.includes(query) ||
             invoiceNumber.includes(query);
    }
  )

  // Calculate purchase totals
  const purchaseTotals = calculatePurchaseTotals(filteredPurchaseData)

  // Process calculated values for purchase
  const purchaseCalculated = calculatePurchaseDerived(purchaseTotals)

  // Calculate sellout totals
  const selloutTotals = calculateSelloutTotals(filteredSelloutData)

  // Calculate derived totals for sellout
  const selloutCalculated = calculateSelloutDerived(selloutTotals)

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-hidden p-4">
          <div className="mb-4 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-xl font-bold">Monthly Tax Summation</h1>
              <p className="text-muted-foreground text-sm">Generate monthly tax summary reports</p>
            </div>
          </div>

          <div className="flex flex-col h-[calc(100vh-120px)] overflow-hidden">
            <Card className="flex-1 overflow-hidden flex flex-col">
              <CardHeader className="py-3 px-4 border-b flex-shrink-0">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-lg">Monthly Tax Report</CardTitle>
                    <CardDescription className="text-xs">Tax details for the selected period</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {months.find((m) => m.value === selectedMonth)?.label} {selectedYear}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 flex-1 overflow-hidden flex flex-col">
                {/* Date selection controls */}
                <div className="flex flex-wrap gap-2 p-3 border-b bg-gray-50 flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Month:</span>
                    <Select
                      value={selectedMonth.toString()}
                      onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                    >
                      <SelectTrigger className="w-[120px] h-8 text-xs">
                        <SelectValue placeholder="Select month" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem key={month.value} value={month.value.toString()}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Year (พ.ศ.):</span>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                    >
                      <SelectTrigger className="w-[100px] h-8 text-xs">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button variant="outline" onClick={fetchTaxData} className="flex items-center gap-1 h-8 text-xs">
                    <span>Enter</span>
                    <ArrowRight className="h-3 w-3" />
                  </Button>

                  <div className="ml-auto relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                    <Input
                      placeholder="Search..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-7 w-[180px] h-8 text-xs"
                    />
                  </div>

                  <Button
                    variant="outline"
                    disabled={activeTab === "purchase" ? purchaseTaxData.length === 0 : selloutTaxData.length === 0}
                    className="h-8 text-xs"
                  >
                    <FileSpreadsheet className="h-3 w-3 mr-1" />
                    Export Excel
                  </Button>

                  <Button
                    variant="outline"
                    disabled={activeTab === "purchase" ? purchaseTaxData.length === 0 : selloutTaxData.length === 0}
                    className="h-8 text-xs"
                  >
                    <FileDown className="h-3 w-3 mr-1" />
                    Export PDF
                  </Button>
                </div>

                {/* Tax Type Tabs */}
                <div className="flex-1 flex flex-col overflow-hidden">
                  <div className="px-4 pt-3 pb-2 flex-shrink-0">
                    <div className="flex h-10 items-center rounded-md bg-muted p-1 text-muted-foreground w-fit">
                      <button
                        className={`flex-1 items-center justify-center whitespace-nowrap rounded-sm px-4 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                          activeTab === "purchase"
                            ? "bg-background text-foreground shadow-sm"
                            : "hover:bg-background/50"
                        }`}
                        onClick={() => setActiveTab("purchase")}
                      >
                        Purchase Tax
                      </button>
                      <button
                        className={`flex-1 items-center justify-center whitespace-nowrap rounded-sm px-4 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                          activeTab === "sellout" ? "bg-background text-foreground shadow-sm" : "hover:bg-background/50"
                        }`}
                        onClick={() => setActiveTab("sellout")}
                      >
                        Sellout Tax
                      </button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-hidden px-4 mt-3">
                    {activeTab === "purchase" && (
                      <div className="flex flex-col h-full">
                        {/* Tax Summary Calculations for Purchase */}
                        <div className="bg-gray-50 p-3 rounded-md border mb-3 flex-shrink-0">
                          <h3 className="text-sm font-medium mb-2">Tax Summary Calculations</h3>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Purchase Price</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseTotals.total_purchase_price)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Purchase VAT</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseTotals.total_purchase_vat_percent)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Operation Cost</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseTotals.total_operation_cost)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">VAT Operation</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseCalculated.vat_operation)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">VAT Transport 1</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseCalculated.vat_transport_1)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">VAT Initial Check</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseCalculated.vat_initial_check)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">VAT Other Costs</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(purchaseCalculated.vat_other_costs_seven)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">VAT Purchase</div>
                              <div className="text-sm font-bold text-primary">
                                {formatCurrency(purchaseCalculated.vat_purchase)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Net VAT Purchase</div>
                              <div className="text-sm font-bold text-primary">
                                {formatCurrency(purchaseCalculated.net_vat_purchase)}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Table Container for Purchase */}
                        <div className="flex-1 overflow-hidden border rounded-md">
                          {isLoading ? (
                            <div className="h-full flex items-center justify-center">
                              <div className="animate-pulse text-muted-foreground">Loading tax data...</div>
                            </div>
                          ) : filteredPurchaseData.length === 0 ? (
                            <div className="h-full flex items-center justify-center">
                              <div className="text-muted-foreground">
                                No purchase tax data available for this period.
                              </div>
                            </div>
                          ) : (
                            <ScrollArea className="h-full max-h-[calc(100vh-400px)]">
                              <div className="min-w-max">
                                <Table className="border-collapse">
                                  <TableHeader className="sticky top-0 bg-white z-10">
                                    <TableRow>

                                      <TableHead className="w-[120px] border text-white">Purchase Date</TableHead>
                                      <TableHead className="w-[100px] border text-white">Brand</TableHead>
                                      <TableHead className="w-[100px] border text-white">Model</TableHead>
                                      <TableHead className="w-[100px] border text-white">Color</TableHead>
                                      <TableHead className="w-[80px] border text-white">Year</TableHead>
                                      <TableHead className="w-[120px] border text-white">License Plate</TableHead>
                                      <TableHead className="w-[80px] border text-white">VAT %</TableHead>
                                      <TableHead className="w-[120px] border text-white">Purchase Price</TableHead>
                                      <TableHead className="w-[120px] border text-white">Purchase VAT</TableHead>
                                      <TableHead className="w-[120px] border text-white">Operation Cost</TableHead>
                                      <TableHead className="w-[120px] border text-white">Transport 1</TableHead>
                                      <TableHead className="w-[120px] border text-white">Initial Check</TableHead>
                                      <TableHead className="w-[120px] border text-white">Tax Insurance</TableHead>
                                      <TableHead className="w-[120px] border text-white">Other Costs</TableHead>
                                      <TableHead className="w-[120px] border text-white">5.3% Tax</TableHead>
                                      <TableHead className="w-[120px] border text-white">Total Purchase Cost</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {filteredPurchaseData.map((item) => (
                                      <TableRow key={item.id}>

                                        <TableCell className="border">{formatTaxDate(item.purchase_date)}</TableCell>
                                        <TableCell className="border">{item.brand}</TableCell>
                                        <TableCell className="border">{item.model}</TableCell>
                                        <TableCell className="border">{item.color}</TableCell>
                                        <TableCell className="border">{item.year}</TableCell>
                                        <TableCell className="border">{item.old_license_plate}</TableCell>
                                        <TableCell className="border">{item.vat_percent}%</TableCell>
                                        <TableCell className="border">{formatCurrency(item.purchase_price)}</TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.purchase_vat_percent)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.operation_cost_incl_vat)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.transport_1_auction_lot)}
                                        </TableCell>
                                        <TableCell className="border">{formatCurrency(item.initial_check)}</TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.tax_insurance_cost_zero)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.other_costs_seven)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.five_three_tax_percentage)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.total_purchase_cost)}
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            </ScrollArea>
                          )}
                        </div>
                      </div>
                    )}

                    {activeTab === "sellout" && (
                      <div className="flex flex-col h-full">
                        {/* Tax Summary Calculations for Sellout */}
                        <div className="bg-gray-50 p-3 rounded-md border mb-3 flex-shrink-0">
                          <h3 className="text-sm font-medium mb-2">Tax Summary Calculations</h3>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Car Amount</div>
                              <div className="text-sm font-bold">{formatCurrency(selloutTotals.total_car_amount)}</div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total VAT on Car Amount</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(selloutTotals.total_vat_on_car_amount)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Commission of Car</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(selloutTotals.total_commission_of_car)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Input VAT Commission</div>
                              <div className="text-sm font-bold">
                                {formatCurrency(selloutTotals.total_input_vat_comm)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Total Purchase VAT</div>
                              <div className="text-sm font-bold text-primary">
                                {formatCurrency(selloutCalculated.total_purchase_vat)}
                              </div>
                            </div>
                            <div className="bg-white p-2 rounded-md border">
                              <div className="text-xs text-muted-foreground">Net Purchase VAT</div>
                              <div className="text-sm font-bold text-primary">
                                {formatCurrency(selloutCalculated.net_purchase_vat)}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Table Container for Sellout */}
                        <div className="flex-1 overflow-hidden border rounded-md">
                          {isLoading ? (
                            <div className="h-full flex items-center justify-center">
                              <div className="animate-pulse text-muted-foreground">Loading tax data...</div>
                            </div>
                          ) : filteredSelloutData.length === 0 ? (
                            <div className="h-full flex items-center justify-center">
                              <div className="text-muted-foreground">
                                No tax invoice data available for this period.
                              </div>
                            </div>
                          ) : (
                            <ScrollArea className="h-full max-h-[calc(100vh-400px)]">
                              <div className="min-w-max">
                                <Table className="border-collapse">
                                  <TableHeader className="sticky top-0 bg-white z-10">
                                    <TableRow>

                                      <TableHead className="w-[120px] border text-white">Sale Date</TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Finance Received Date
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Car Tax Invoice Date
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Car Tax Invoice Number
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">Car Amount</TableHead>
                                      <TableHead className="w-[120px] border text-white">Car VAT Amount</TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Commission Tax Invoice Date
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Commission Tax Invoice Number
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Car Commission Amount
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">
                                        Input VAT Commission
                                      </TableHead>
                                      <TableHead className="w-[120px] border text-white">Withholding Tax</TableHead>
                                      <TableHead className="w-[80px] border text-white">VAT %</TableHead>
                                      <TableHead className="w-[100px] border text-white">Tank #</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {filteredSelloutData.map((item) => (
                                      <TableRow key={item.id}>

                                        <TableCell className="border">{formatTaxDate(item.sale_date)}</TableCell>
                                        <TableCell className="border">
                                          {formatTaxDate(item.finance_received_date)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatTaxDate(item.car_tax_invoice_date)}
                                        </TableCell>
                                        <TableCell className="border">{item.car_tax_invoice_number}</TableCell>
                                        <TableCell className="border">{formatCurrency(item.car_amount)}</TableCell>
                                        <TableCell className="border">{formatCurrency(item.car_vat_amount)}</TableCell>
                                        <TableCell className="border">
                                          {formatTaxDate(item.commission_tax_invoice_date)}
                                        </TableCell>
                                        <TableCell className="border">{item.commission_tax_invoice_number}</TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.car_commission_amount)}
                                        </TableCell>
                                        <TableCell className="border">
                                          {formatCurrency(item.input_vat_commission)}
                                        </TableCell>
                                        <TableCell className="border">{formatCurrency(item.withholding_tax)}</TableCell>
                                        <TableCell className="border">{item.vat_percent}%</TableCell>
                                        <TableCell className="border">{item.tank_number}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            </ScrollArea>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
