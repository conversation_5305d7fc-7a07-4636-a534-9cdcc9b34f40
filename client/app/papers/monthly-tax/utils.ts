/**
 * Utility functions for the monthly tax page
 */

/**
 * Format a date string for display in the monthly tax page
 * @param dateString Date string to format
 * @returns Formatted date string (DD/MM/YYYY) or empty string if invalid
 */
export function formatTaxDate(dateString: string | null | undefined): string {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }
    
    // Format as DD/MM/YYYY
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting tax date:", error);
    return "";
  }
}

/**
 * Calculate purchase tax totals
 * @param data Purchase tax data array
 * @returns Object with calculated totals
 */
export function calculatePurchaseTotals(data: any[]) {
  return {
    total_purchase_price: data.reduce((sum, item) => sum + (item.purchase_price || 0), 0),
    total_purchase_vat_percent: data.reduce((sum, item) => sum + (item.purchase_vat_percent || 0), 0),
    total_operation_cost: data.reduce((sum, item) => sum + (item.operation_cost_incl_vat || 0), 0),
    total_transport_1_auction_lot: data.reduce((sum, item) => sum + (item.transport_1_auction_lot || 0), 0),
    total_initial_check: data.reduce((sum, item) => sum + (item.initial_check || 0), 0),
    total_tax_insurance_cost_zero: data.reduce((sum, item) => sum + (item.tax_insurance_cost_zero || 0), 0),
    total_other_costs_seven: data.reduce((sum, item) => sum + (item.other_costs_seven || 0), 0),
    total_five_three_tax_percentage: data.reduce(
      (sum, item) => sum + (item.five_three_tax_percentage || 0),
      0,
    ),
    total_total_purchase_cost: data.reduce((sum, item) => sum + (item.total_purchase_cost || 0), 0),
  };
}

/**
 * Calculate purchase tax derived values
 * @param totals Purchase tax totals
 * @returns Object with calculated values
 */
export function calculatePurchaseDerived(totals: any) {
  return {
    vat_operation: totals.total_operation_cost * 0.07,
    vat_transport_1: totals.total_transport_1_auction_lot * 0.07,
    vat_initial_check: totals.total_initial_check * 0.07,
    vat_other_costs_seven: totals.total_other_costs_seven * 0.07,
    vat_purchase: totals.total_purchase_vat_percent,
    net_vat_purchase: totals.total_purchase_vat_percent - 
      (totals.total_operation_cost * 0.07) - 
      (totals.total_transport_1_auction_lot * 0.07) - 
      (totals.total_initial_check * 0.07) - 
      (totals.total_other_costs_seven * 0.07),
  };
}

/**
 * Calculate sellout tax totals
 * @param data Sellout tax data array
 * @returns Object with calculated totals
 */
export function calculateSelloutTotals(data: any[]) {
  return {
    total_car_amount: data.reduce((sum, item) => sum + (item.car_amount || 0), 0),
    total_vat_on_car_amount: data.reduce((sum, item) => sum + (item.car_vat_amount || 0), 0),
    total_commission_of_car: data.reduce((sum, item) => sum + (item.car_commission_amount || 0), 0),
    total_input_vat_comm: data.reduce((sum, item) => sum + (item.input_vat_commission || 0), 0),
  };
}

/**
 * Calculate sellout tax derived values
 * @param totals Sellout tax totals
 * @returns Object with calculated values
 */
export function calculateSelloutDerived(totals: any) {
  return {
    total_purchase_vat: totals.total_vat_on_car_amount,
    net_purchase_vat: totals.total_vat_on_car_amount - totals.total_input_vat_comm,
  };
}
