"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, FileDown, Printer, Search } from "lucide-react"
import Link from "next/link"
import { DailyAuctionSummary } from "@/components/papers/daily-auction-summary"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"

export default function DailyAuctionPage() {
  // Initialize with current date in Buddhist Era format (YYYY-MM-DD)
  const currentDate = new Date()
  const buddhistYear = currentDate.getFullYear() + 543
  // const gregorianYear = currentDate.getFullYear()
  const month = String(currentDate.getMonth() + 1).padStart(2, '0')
  const day = String(currentDate.getDate()).padStart(2, '0')
  const initialDate = `${buddhistYear}-${month}-${day}`

  console.log("Initial date", initialDate)

  const [selectedDate, setSelectedDate] = useState(initialDate )
  const [filterLocation, setFilterLocation] = useState("")

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Daily Auction Summary</h1>
              <p className="text-muted-foreground">Create daily summary reports for auction activities</p>
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Generate Daily Auction Summary</CardTitle>
              <CardDescription>Select a date to generate the auction summary</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 items-end">
                <div className="space-y-2 w-[200px]">
                  <ThaiDatePickerWrapper
                    id="date"
                    label="Date"
                    value={selectedDate}
                    onChange={setSelectedDate}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="auction-location">Auction Location (Optional)</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="auction-location"
                      placeholder="Filter by location"
                      className="pl-8 w-[250px]"
                      value={filterLocation}
                      onChange={(e) => setFilterLocation(e.target.value)}
                    />
                  </div>
                </div>

                <div className="ml-auto flex gap-2">
                  <Button variant="outline">
                    <FileDown className="mr-2 h-4 w-4" />
                    Export PDF
                  </Button>
                  <Button variant="outline">
                    <Printer className="mr-2 h-4 w-4" />
                    Print
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Daily Auction Summary Report</CardTitle>
              <CardDescription>
                Summary of vehicles purchased at auction on {selectedDate ? (() => {
                  // Create a new date from selectedDate but subtract 543 years to convert from Buddhist Era to Gregorian
                  const dateParts = selectedDate.split('-');
                  const buddhistYear = parseInt(dateParts[0]);
                  const gregorianYear = buddhistYear - 543;
                  const gregorianDate = new Date(`${gregorianYear}-${dateParts[1]}-${dateParts[2]}`);

                  return gregorianDate.toLocaleDateString('th-TH', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  });
                })() : ''}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DailyAuctionSummary selectedDate={selectedDate} />
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
